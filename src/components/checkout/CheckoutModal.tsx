import React, { useEffect, useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Loader2, Shield, Lock, CreditCard } from "lucide-react";
import { Analytics } from '@/utils/analytics';
import { useCheckoutIframe } from './CheckoutIframeManager';

interface CheckoutModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  planId?: string;
  theme?: 'light' | 'dark' | 'system';
}

export const CheckoutModal: React.FC<CheckoutModalProps> = ({
  isOpen,
  onOpenChange,
  planId = "plan_NCr6hVh2qtYBb", // Your actual Whop plan ID
  theme = 'dark',
}) => {
  const [showModal, setShowModal] = useState(false);
  const { showIframe, hideIframe, isReady } = useCheckoutIframe();

  useEffect(() => {
    if (isOpen && isReady) {
      // Show the preloaded iframe instantly
      showIframe();
      setShowModal(true);
      
      // Track checkout modal opened
      Analytics.trackEvent('checkout_modal_opened', {
        plan_id: planId,
        theme: theme,
        instant: true
      });
    } else if (isOpen && !isReady) {
      // Fallback: show modal with loading state
      setShowModal(true);
      
      Analytics.trackEvent('checkout_modal_opened', {
        plan_id: planId,
        theme: theme,
        instant: false
      });
    } else if (!isOpen) {
      // Hide iframe and modal
      hideIframe();
      setShowModal(false);
    }
  }, [isOpen, isReady, planId, theme, showIframe, hideIframe]);

  // Listen for messages from the iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'checkout-complete') {
        // Track checkout completion
        Analytics.trackEvent('checkout_completed', {
          plan_id: planId,
        });
        
        // Close the modal
        onOpenChange(false);
        
        // Optionally redirect or show success message
        // window.location.href = '/success';
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [planId, onOpenChange]);

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Track checkout modal closed
      Analytics.trackEvent('checkout_modal_closed', {
        plan_id: planId,
      });
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[700px] md:max-w-[800px] lg:max-w-[900px] max-h-[95vh] p-0 bg-black/95 border border-gold/20 backdrop-blur-xl animate-in fade-in-0 zoom-in-95 duration-300 flex flex-col">
        {/* Gradient background effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-gold/10 via-transparent to-gold/5 pointer-events-none rounded-lg" />
        
        <DialogHeader className="relative z-10 p-6 pb-4 border-b border-white/10 shrink-0">
          <DialogTitle className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Start Your 3-Day Free Trial
          </DialogTitle>
          <DialogDescription className="text-base text-gray-400 mt-2">
            Join thousands of profitable bettors. Cancel anytime during your trial.
          </DialogDescription>
          
          {/* Development notice */}
          {import.meta.env.DEV && (
            <div className="mt-3 p-2 bg-yellow-500/10 border border-yellow-500/20 rounded text-xs text-yellow-500/80">
              <strong>Dev Note:</strong> Autofill disabled on localhost. This works normally on production (HTTPS).
            </div>
          )}
          
          {/* Features list */}
          <div className="flex flex-wrap gap-4 mt-4 text-sm">
            <div className="flex items-center gap-1.5">
              <svg className="w-4 h-4 text-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-gray-300">Instant access</span>
            </div>
            <div className="flex items-center gap-1.5">
              <svg className="w-4 h-4 text-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-gray-300">Cancel anytime</span>
            </div>
            <div className="flex items-center gap-1.5">
              <svg className="w-4 h-4 text-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-gray-300">No hidden fees</span>
            </div>
          </div>
        </DialogHeader>
        
        {/* Iframe container - only show loading if iframe not ready */}
        <div id="checkout-iframe-container" className="relative bg-black overflow-hidden flex-1" style={{ minHeight: '750px' }}>
          {!isReady && showModal && (
            <div className="absolute inset-0 bg-black z-20 flex items-center justify-center">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-gold mx-auto mb-4" />
                <p className="text-sm text-gray-400">Preparing checkout...</p>
              </div>
            </div>
          )}
          
          {/* The iframe is managed by CheckoutIframeManager and will appear here when ready */}
        </div>
        
        {/* Trust badges */}
        <div className="relative z-10 p-4 border-t border-white/10 bg-black/50 shrink-0">
          <div className="flex items-center justify-center gap-6 text-xs text-gray-500">
            <div className="flex items-center gap-1.5">
              <Shield className="w-4 h-4" />
              <span>Secure checkout</span>
            </div>
            <div className="flex items-center gap-1.5">
              <Lock className="w-4 h-4" />
              <span>SSL encrypted</span>
            </div>
            <div className="flex items-center gap-1.5">
              <CreditCard className="w-4 h-4" />
              <span>Powered by Whop</span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};