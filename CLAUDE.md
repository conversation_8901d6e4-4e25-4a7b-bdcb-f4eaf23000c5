# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Build for development (with source maps)
npm run build:dev

# Preview production build locally
npm run preview

# Run linting
npm run lint

# Generate blog content (runs automatically before build)
node scripts/build-blog-content.js
```

## Feature Implementation System Guidelines

### Feature Implementation Priority Rules
- IMMEDIATE EXECUTION: Launch parallel Tasks immediately upon feature requests
- NO CLARIFICATION: Ski<PERSON> asking what type of implementation unless absolutely critical
- PARALLEL BY DEFAULT: Always use 7-parallel-Task method for efficiency

### Parallel Feature Implementation Workflow
1. **Component**: Create main component file
2. **Styles**: Create component styles/CSS
3. **Tests**: Create test files  
4. **Types**: Create type definitions
5. **Hooks**: Create custom hooks/utilities
6. **Integration**: Update routing, imports, exports
7. **Remaining**: Update package.json, documentation, configuration files
8. **Review and Validation**: Coordinate integration, run tests, verify build, check for conflicts

### Context Optimization Rules
- Strip out all comments when reading code files for analysis
- Each task handles ONLY specified files or file types
- Task 7 combines small config/doc updates to prevent over-splitting

### Feature Implementation Guidelines
- **CRITICAL**: Make MINIMAL CHANGES to existing patterns and structures
- **CRITICAL**: Preserve existing naming conventions and file organization
- Follow project's established architecture and component patterns
- Use existing utility functions and avoid duplicating functionality

**Note:** No test commands are configured. The project doesn't have a testing framework set up.

## High-Level Architecture

This is a React-based sports betting analytics website built with:
- **React 18** with TypeScript for type safety
- **Vite** as the build tool
- **Tailwind CSS** with shadcn/ui component library
- **React Router v7** for client-side routing
- **TanStack Query** for server state management
- **PostHog**, Vercel Analytics for tracking

### Key Architectural Patterns

1. **Component Structure**: Components are organized by feature in `/src/components/` with UI primitives in `/src/components/ui/`
2. **Lazy Loading**: Routes use React.lazy() for code splitting
3. **Markdown-based Blog**: Blog posts are stored as Markdown files in `/src/content/blog/` with frontmatter metadata
4. **Type Safety**: Full TypeScript coverage with strict mode enabled
5. **Path Aliases**: Use `@/*` to import from `src/*` directory

### Project Structure Overview

- **Pages** (`/src/pages/`): Route-level components (Index, Blog, BlogIndex, BettingSimulator)
- **Blog System**: Markdown files parsed with gray-matter and marked libraries
- **Styling**: Modular CSS approach with separate files for animations, effects, and utilities
- **Assets**: Self-hosted fonts and optimized images in `/public/`

### Important Context

- This is a Lovable project (originally created via lovable.dev)
- The site is deployed on Netlify with SPA fallback configuration
- PostHog analytics is integrated with specific project credentials
- The project includes a planned CMS workflow (see cms.md) but not yet implemented

### Performance Considerations

- Images are optimized to WebP format
- Fonts are self-hosted and preloaded
- Components use intersection observer for lazy loading
- CSS animations are GPU-accelerated where possible

### Recent Performance Optimizations

1. **Build-Time Blog Processing**: All markdown parsing now happens at build time, generating static JSON files
2. **Service Worker**: Fixed asset paths for proper caching of fonts and blog content
3. **CSS Specificity**: Removed all !important usage in favor of proper cascade management
4. **Progressive Rendering**: Added `useProgressiveRender` hook for optimized content loading
5. **Idle Callbacks**: Using requestIdleCallback for non-critical deferred work
6. **Code Splitting**: Fixed Footer component to properly code-split across all pages
7. **Removed Unused Code**: Deleted parseMarkdown.ts after moving to build-time processing
8. **SEO Improvements**: Implemented react-helmet-async in GuideDetail for proper meta tag management
9. **Analytics Optimization**: Lazy-loaded PostHog with reduced features to improve initial load time
10. **Navigation Styling**: Fixed double underline issues and vertical alignment in navbar
11. **Font Optimization**: Removed duplicate local font files and references, now using only Google Fonts CDN
12. **Type Safety**: Added proper TypeScript declarations for PostHog, removed @ts-ignore
13. **Analytics Improvements**: Added event queueing for PostHog to prevent losing early events
14. **Feature Flags**: Added async-safe methods (getFeatureFlag, onFeatureFlag) for handling feature flags
15. **Modern APIs**: Replaced deprecated performance.navigation.type with Navigation Timing API
16. **Hyros Tracking**: Added Hyros analytics tracking script to index.html
17. **Whop Checkout Integration**: Implemented embedded checkout modal system
    - Created CheckoutModal component using Dialog UI
    - Added CheckoutProvider for global checkout state management
    - Updated all CTA buttons to open checkout modal instead of external links
    - Integrated analytics tracking for checkout events
    - Responsive design with loading states